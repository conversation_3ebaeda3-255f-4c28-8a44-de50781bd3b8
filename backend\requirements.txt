flask==2.3.3
flask-cors==4.0.0
flask-socketio==5.3.6
numpy==1.24.3
tensorflow-cpu==2.15.0
librosa==0.10.1
# Removing pyaudio as it causes build issues in cloud environments
# pyaudio==0.2.13
matplotlib==3.7.2
python-engineio==4.8.0
python-socketio==5.10.0
eventlet==0.33.3
# Use a lighter version of torch
--find-links https://download.pytorch.org/whl/torch_stable.html
torch==2.1.0+cpu
gunicorn==21.2.0
soundfile==0.12.1
scipy==1.11.3
