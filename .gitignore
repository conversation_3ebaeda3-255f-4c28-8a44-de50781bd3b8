# Python backend
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.env
.venv
venv/
ENV/

# JavaScript/TypeScript frontend
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
/dist
/build
/.vite
.cache/

# Model files (often large)
# Uncomment the following lines if you don't want to track model files
# *.h5
# *.pth
# *.onnx
# *.pb
# models/

# Editor configs
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific
.DS_Store
Thumbs.db 